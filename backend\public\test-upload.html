<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MealMate File Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .form-section {
            border: 1px solid #ccc;
            padding: 20px;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .response {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
        .token-input {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>MealMate File Upload Test</h1>
    
    <div class="token-input">
        <label for="auth-token">Authentication Token:</label>
        <input type="text" id="auth-token" placeholder="Enter your JWT token here">
    </div>
    
    <div class="container">
        <!-- User Profile Photo Upload -->
        <div class="form-section">
            <h2>Test User Profile Photo Upload</h2>
            <form id="profile-photo-form">
                <div class="form-group">
                    <label for="user-id">User ID:</label>
                    <input type="text" id="user-id" required>
                </div>
                <div class="form-group">
                    <label for="profile-photo">Profile Photo:</label>
                    <input type="file" id="profile-photo" name="profilePhoto" accept="image/*" required>
                </div>
                <button type="submit">Upload Profile Photo</button>
            </form>
            <div class="response" id="profile-response"></div>
        </div>
        
        <!-- Review Photos Upload -->
        <div class="form-section">
            <h2>Test Review Photos Upload</h2>
            <form id="review-form">
                <div class="form-group">
                    <label for="meal-id">Meal ID:</label>
                    <input type="text" id="meal-id" required>
                </div>
                <div class="form-group">
                    <label for="rating">Rating:</label>
                    <input type="number" id="rating" min="1" max="5" required>
                </div>
                <div class="form-group">
                    <label for="review-text">Review Text:</label>
                    <textarea id="review-text" rows="4" required></textarea>
                </div>
                <div class="form-group">
                    <label for="review-photos">Review Photos (max 5):</label>
                    <input type="file" id="review-photos" name="reviewPhotos" accept="image/*" multiple required>
                </div>
                <button type="submit">Submit Review with Photos</button>
            </form>
            <div class="response" id="review-response"></div>
        </div>
    </div>

    <script>
        // Profile Photo Upload
        document.getElementById('profile-photo-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const userId = document.getElementById('user-id').value;
            const profilePhotoInput = document.getElementById('profile-photo');
            const token = document.getElementById('auth-token').value;
            const responseDiv = document.getElementById('profile-response');
            
            if (!token) {
                responseDiv.innerHTML = 'Please enter an authentication token';
                return;
            }
            
            const formData = new FormData();
            formData.append('profilePhoto', profilePhotoInput.files[0]);
            
            try {
                responseDiv.innerHTML = 'Uploading...';
                
                const response = await fetch(`/api/users/${userId}/profile-photo`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    responseDiv.innerHTML = `<p>Success! Profile photo updated.</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    responseDiv.innerHTML = `<p>Error: ${data.error || 'Unknown error'}</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                responseDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            }
        });
        
        // Review with Photos Upload
        document.getElementById('review-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const mealId = document.getElementById('meal-id').value;
            const rating = document.getElementById('rating').value;
            const reviewText = document.getElementById('review-text').value;
            const reviewPhotosInput = document.getElementById('review-photos');
            const token = document.getElementById('auth-token').value;
            const responseDiv = document.getElementById('review-response');
            
            if (!token) {
                responseDiv.innerHTML = 'Please enter an authentication token';
                return;
            }
            
            const formData = new FormData();
            formData.append('rating', rating);
            formData.append('text', reviewText);
            
            // Add all selected photos
            for (let i = 0; i < reviewPhotosInput.files.length; i++) {
                formData.append('reviewPhotos', reviewPhotosInput.files[i]);
            }
            
            try {
                responseDiv.innerHTML = 'Submitting review...';
                
                const response = await fetch(`/api/meals/${mealId}/reviews`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    responseDiv.innerHTML = `<p>Success! Review submitted with photos.</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    responseDiv.innerHTML = `<p>Error: ${data.error || 'Unknown error'}</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                responseDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
