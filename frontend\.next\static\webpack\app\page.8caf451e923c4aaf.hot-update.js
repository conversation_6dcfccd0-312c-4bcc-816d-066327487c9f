"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MealMatePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/CartContext */ \"(app-pages-browser)/./contexts/CartContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ChefHat,Heart,Play,Search,ShoppingCart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1,\n            delayChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        y: 20,\n        opacity: 0\n    },\n    visible: {\n        y: 0,\n        opacity: 1,\n        transition: {\n            type: \"spring\",\n            stiffness: 100,\n            damping: 10\n        }\n    }\n};\nconst floatingVariants = {\n    animate: {\n        y: [\n            -10,\n            10,\n            -10\n        ],\n        transition: {\n            duration: 3,\n            repeat: Number.POSITIVE_INFINITY,\n            ease: \"easeInOut\"\n        }\n    }\n};\nconst stats = [\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: \"Home Chefs\",\n        value: \"500+\",\n        color: \"text-orange-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: \"Happy Customers\",\n        value: \"10K+\",\n        color: \"text-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        label: \"Orders Delivered\",\n        value: \"50K+\",\n        color: \"text-green-500\"\n    }\n];\nfunction MealMatePage() {\n    _s();\n    const { addToCart } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__.useCart)();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [likedMeals, setLikedMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [meals, setMeals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n    const categories = [\n        \"all\",\n        \"breakfast\",\n        \"lunch\",\n        \"dinner\",\n        \"snacks\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchFeaturedMeals();\n    }, []);\n    const fetchFeaturedMeals = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/meals\"));\n            if (response.ok) {\n                const data = await response.json();\n                // Get first 6 available meals for featured section\n                const availableMeals = data.data.filter((meal)=>meal.availability).slice(0, 6);\n                setMeals(availableMeals);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch meals:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const toggleLike = (mealId)=>{\n        setLikedMeals((prev)=>prev.includes(mealId) ? prev.filter((id)=>id !== mealId) : [\n                ...prev,\n                mealId\n            ]);\n    };\n    const handleAddToCart = (meal)=>{\n        addToCart({\n            _id: meal._id,\n            name: meal.name,\n            price: meal.price,\n            category: meal.category,\n            provider: meal.provider\n        });\n    };\n    const filteredMeals = meals.filter((meal)=>{\n        const matchesSearch = meal.name.toLowerCase().includes(searchQuery.toLowerCase()) || meal.description.toLowerCase().includes(searchQuery.toLowerCase()) || meal.provider.name.toLowerCase().includes(searchQuery.toLowerCase());\n        const matchesCategory = selectedCategory === \"all\" || meal.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"relative overflow-hidden bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/20\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative container mx-auto px-4 py-20 lg:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            className: \"max-w-4xl mx-auto text-center\",\n                            variants: containerVariants,\n                            initial: \"hidden\",\n                            animate: \"visible\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            className: \"bg-white/20 text-white border-white/30 mb-4\",\n                                            children: \"\\uD83C\\uDF7D️ Homemade with Love\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-orange-100 bg-clip-text text-transparent\",\n                                            children: [\n                                                \"Discover Authentic\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-300\",\n                                                    children: \"Home Cooking\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                    variants: itemVariants,\n                                    className: \"text-xl md:text-2xl mb-8 text-orange-100 max-w-2xl mx-auto\",\n                                    children: \"Connect with local home chefs and enjoy fresh, homemade meals delivered to your doorstep\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-white text-orange-600 hover:bg-orange-50 transform hover:scale-105 transition-all duration-200 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Order Now\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"border-white text-orange-600 hover:bg-orange-50 hover:text-orange-600 transform hover:scale-105 transition-all duration-200\",\n                                            children: [\n                                                \"Become a Chef\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-20 left-10 hidden lg:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        variants: floatingVariants,\n                                        animate: \"animate\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-20 right-10 hidden lg:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        variants: floatingVariants,\n                                        animate: \"animate\",\n                                        transition: {\n                                            delay: 1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-10 h-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-16 bg-white\",\n                initial: {\n                    opacity: 0,\n                    y: 50\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4 \".concat(stat.color),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"w-8 h-8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-12 bg-gray-50\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"max-w-4xl mx-auto\",\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"text-3xl md:text-4xl font-bold text-center mb-8 text-gray-900\",\n                                children: \"Find Your Perfect Meal\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search for meals, cuisines, or chefs...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-12 pr-4 py-4 text-lg rounded-full border-2 border-gray-200 focus:border-orange-500 transition-colors duration-200\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                className: \"flex flex-wrap gap-3 justify-center\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: selectedCategory === category ? \"default\" : \"outline\",\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"capitalize rounded-full transition-all duration-200 \".concat(selectedCategory === category ? \"bg-orange-500 hover:bg-orange-600 transform scale-105\" : \"hover:bg-orange-50 hover:border-orange-300\"),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-16\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                            mode: \"wait\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                variants: containerVariants,\n                                initial: \"hidden\",\n                                animate: \"visible\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-full flex justify-center py-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, this) : filteredMeals.map((meal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        variants: itemVariants,\n                                        whileHover: {\n                                            y: -10,\n                                            scale: 1.02\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 300,\n                                            damping: 20\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-white h-full flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-48 bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center overflow-hidden\",\n                                                            children: meal.photo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: meal.photo,\n                                                                alt: meal.name,\n                                                                className: \"w-full h-full object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-6xl\",\n                                                                children: \"\\uD83C\\uDF7D️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                                            className: \"absolute top-4 right-4 p-2 rounded-full bg-white/90 backdrop-blur-sm shadow-lg\",\n                                                            onClick: ()=>toggleLike(meal._id),\n                                                            whileHover: {\n                                                                scale: 1.1\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.9\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 h-5 transition-colors duration-200 \".concat(likedMeals.includes(meal._id) ? \"text-red-500 fill-red-500\" : \"text-gray-600\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            className: \"absolute top-4 left-4 bg-orange-500 text-white capitalize\",\n                                                            children: meal.category\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6 flex-1 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 line-clamp-1 flex-1 mr-2\",\n                                                                    children: meal.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-orange-600 whitespace-nowrap\",\n                                                                    children: [\n                                                                        \"₹\",\n                                                                        meal.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4 line-clamp-2 flex-1\",\n                                                            children: meal.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"by \",\n                                                                            meal.provider.name\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.95\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: ()=>handleAddToCart(meal),\n                                                                        className: \"bg-orange-500 hover:bg-orange-600 text-white rounded-full px-6 w-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-4 h-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Add to Cart\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, meal._id, false, {\n                                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 19\n                                    }, this))\n                            }, selectedCategory + searchQuery, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this),\n                        !loading && filteredMeals.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            className: \"text-center py-16\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                    children: \"No meals found\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Try adjusting your search or filters\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.section, {\n                className: \"py-20 bg-gradient-to-r from-orange-500 to-red-500 text-white\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.8\n                },\n                viewport: {\n                    once: true\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                variants: itemVariants,\n                                className: \"text-3xl md:text-5xl font-bold mb-6\",\n                                children: \"Ready to Start Cooking?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                variants: itemVariants,\n                                className: \"text-xl mb-8 text-orange-100 max-w-2xl mx-auto\",\n                                children: \"Join our community of home chefs and start earning by sharing your delicious homemade meals\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: itemVariants,\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"lg\",\n                                    className: \"bg-white text-orange-600 hover:bg-orange-50 px-8 py-4 text-lg rounded-full shadow-lg\",\n                                    children: [\n                                        \"Become a Chef Partner\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ChefHat_Heart_Play_Search_ShoppingCart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-5 h-5 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\MealMate\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(MealMatePage, \"76bbDxunDAuOi11yoX9H+/I5UBk=\", false, function() {\n    return [\n        _contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__.useCart\n    ];\n});\n_c = MealMatePage;\nvar _c;\n$RefreshReg$(_c, \"MealMatePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});