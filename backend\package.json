{"name": "mealmate-backend", "version": "1.0.0", "description": "Backend API for MealMate - connecting users with homemade meal/tiffin services", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["mealmate", "food", "tiffin", "homemade", "meals", "api"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "mongodb": "^6.16.0", "mongoose": "^7.0.3", "multer": "^1.4.5-lts.2", "multer-storage-cloudinary": "^4.0.0", "punycode": "^2.3.1", "validator": "^13.9.0"}, "devDependencies": {"axios": "^1.9.0", "form-data": "^4.0.2", "jest": "^29.5.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.1.10", "supertest": "^6.3.3"}}